using System;
using System.Runtime.InteropServices;
using System.Text;
using JYJ001.App.Services.Common.Interfaces;
using WaferAligner.EventIds;
using WaferAligner.Communication.Serial.Interfaces;
using JYJ001.App.Services.Common.Extension;

namespace WaferAligner.Communication.Serial.Implementation
{
    /// <summary>
    /// SerialCom.dll封装类
    /// </summary>
    public class SerialComWrapper : ISerialComWrapper
    {
        private readonly ILoggingService _loggingService;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="loggingService">日志服务</param>
        public SerialComWrapper(ILoggingService loggingService)
        {
            _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
            _loggingService.LogDebug("创建SerialComWrapper", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
        }

        #region DLL导入

        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 GetDLLVersion();
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_OpenQK(Int32 controlNum);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_OpenCom(Int32 Com, Int32 Baud);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_CloseCom();
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_SetControlAxis(UInt32 Axis_Shift);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_ReadPosition(UInt32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_AxisEnable(UInt32 Address, char KG);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_PositionAbsoluteMove(UInt32 Address, Int32 Position);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_PositionRelativeMove(UInt32 Address, Int32 Position);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_AxisStop(UInt32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_GetAxisStatus(Int32 Address, Int32 StatusNum);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_ClearMotorError(Int32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_SetSpeed(UInt32 Address, char Type, UInt32 CountsPerS);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_GetSpeed(UInt32 Address, char Type);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_SetZeroPosition(UInt32 Address);
        
        [DllImport("SerialCom.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern Int32 DLL_JogMove(UInt32 Address, char Cmd);

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取DLL版本
        /// </summary>
        public int GetVersion()
        {
            try
            {
                var version = GetDLLVersion();
                _loggingService.LogDebug($"获取DLL版本: {version}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return version;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "获取DLL版本失败", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 打开控制器设备
        /// </summary>
        /// <param name="controlNum">控制器编号</param>
        public int OpenDevice(int controlNum)
        {
            try
            {
                var result = DLL_OpenQK(controlNum);
                _loggingService.LogDebug($"打开控制器设备 {controlNum}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"打开控制器设备 {controlNum} 失败", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 打开串口
        /// </summary>
        /// <param name="comPort">串口号</param>
        /// <param name="baudRate">波特率</param>
        public int OpenComPort(int comPort, int baudRate)
        {
            try
            {
                var result = DLL_OpenCom(comPort, baudRate);
                _loggingService.LogDebug($"打开串口 COM{comPort}, 波特率: {baudRate}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"打开串口 COM{comPort} 失败", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 关闭串口
        /// </summary>
        public int CloseComPort()
        {
            try
            {
                var result = DLL_CloseCom();
                _loggingService.LogDebug($"关闭串口, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "关闭串口失败", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 设置控制轴
        /// </summary>
        /// <param name="axisShift">轴位移量</param>
        public int SetControlAxis(uint axisShift)
        {
            try
            {
                var result = DLL_SetControlAxis(axisShift);
                _loggingService.LogDebug($"设置控制轴 {axisShift}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置控制轴 {axisShift} 失败", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 轴使能
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="kg">开关指令 'K'=开启, 'F'=关闭</param>
        public int AxisEnable(uint address, char kg)
        {
            try
            {
                var result = DLL_AxisEnable(address, kg);
                _loggingService.LogDebug($"轴使能 地址:{address}, 指令:{kg}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"轴使能失败 地址:{address}, 指令:{kg}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 读取位置
        /// </summary>
        /// <param name="address">轴地址</param>
        public int ReadPosition(uint address)
        {
            try
            {
                var result = DLL_ReadPosition(address);
                _loggingService.LogDebug($"读取位置 地址:{address}, 位置: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"读取位置失败 地址:{address}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 绝对位置移动
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="position">目标位置</param>
        public int PositionAbsoluteMove(uint address, int position)
        {
            try
            {
                var result = DLL_PositionAbsoluteMove(address, position);
                _loggingService.LogDebug($"绝对位置移动 地址:{address}, 位置:{position}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"绝对位置移动失败 地址:{address}, 位置:{position}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 相对位置移动
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="position">相对位置</param>
        public int PositionRelativeMove(uint address, int position)
        {
            try
            {
                var result = DLL_PositionRelativeMove(address, position);
                _loggingService.LogDebug($"相对位置移动 地址:{address}, 位置:{position}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"相对位置移动失败 地址:{address}, 位置:{position}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 停止轴运动
        /// </summary>
        /// <param name="address">轴地址</param>
        public int AxisStop(uint address)
        {
            try
            {
                var result = DLL_AxisStop(address);
                _loggingService.LogDebug($"停止轴运动 地址:{address}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"停止轴运动失败 地址:{address}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 获取轴状态
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="statusNum">状态编号</param>
        public int GetAxisStatus(int address, int statusNum)
        {
            try
            {
                var result = DLL_GetAxisStatus(address, statusNum);
                _loggingService.LogDebug($"获取轴状态 地址:{address}, 状态编号:{statusNum}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取轴状态失败 地址:{address}, 状态编号:{statusNum}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 清除电机错误
        /// </summary>
        /// <param name="address">轴地址</param>
        public int ClearMotorError(int address)
        {
            try
            {
                var result = DLL_ClearMotorError(address);
                _loggingService.LogDebug($"清除电机错误 地址:{address}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"清除电机错误失败 地址:{address}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 设置速度
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="type">速度类型 'R'=运行速度, 'J'=点动速度</param>
        /// <param name="countsPerS">速度值（计数/秒）</param>
        public int SetSpeed(uint address, char type, uint countsPerS)
        {
            try
            {
                var result = DLL_SetSpeed(address, type, countsPerS);
                _loggingService.LogDebug($"设置速度 地址:{address}, 类型:{type}, 速度:{countsPerS}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置速度失败 地址:{address}, 类型:{type}, 速度:{countsPerS}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 获取速度
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="type">速度类型 'R'=运行速度, 'J'=点动速度</param>
        public int GetSpeed(uint address, char type)
        {
            try
            {
                var result = DLL_GetSpeed(address, type);
                _loggingService.LogDebug($"获取速度 地址:{address}, 类型:{type}, 速度: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"获取速度失败 地址:{address}, 类型:{type}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// 设置零位置
        /// </summary>
        /// <param name="address">轴地址</param>
        public int SetZeroPosition(uint address)
        {
            try
            {
                var result = DLL_SetZeroPosition(address);
                _loggingService.LogDebug($"设置零位置 地址:{address}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"设置零位置失败 地址:{address}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        /// <summary>
        /// JOG移动
        /// </summary>
        /// <param name="address">轴地址</param>
        /// <param name="cmd">命令类型 'F'=正向, 'B'=反向, 'S'=停止</param>
        public int JogMove(uint address, char cmd)
        {
            try
            {
                var result = DLL_JogMove(address, cmd);
                _loggingService.LogDebug($"JOG移动 地址:{address}, 命令:{cmd}, 结果: {result}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return result;
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, $"JOG移动失败 地址:{address}, 命令:{cmd}", WaferAligner.EventIds.EventIds.Serial_Axis_Operation);
                return -1;
            }
        }

        #endregion

        #region IDisposable实现

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        /// <param name="disposing">是否正在释放</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                try
                {
                    if (disposing)
                    {
                        // 释放托管资源
                        CloseComPort();
                        _loggingService.LogDebug("SerialComWrapper资源已释放", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
                    }
                }
                catch (Exception ex)
                {
                    _loggingService.LogError(ex, "释放SerialComWrapper资源时发生错误", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
                }
                finally
                {
                    _disposed = true;
                }
            }
        }

        /// <summary>
        /// 析构函数
        /// </summary>
        ~SerialComWrapper()
        {
            try
            {
                Dispose(false);
            }
            catch (Exception ex)
            {
                _loggingService.LogError(ex, "释放SerialComWrapper资源时发生错误", WaferAligner.EventIds.EventIds.Serial_Resource_Released);
            }
        }

        #endregion
    }
}
