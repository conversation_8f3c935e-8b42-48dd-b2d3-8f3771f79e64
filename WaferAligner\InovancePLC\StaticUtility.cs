﻿using WaferAligner.Communication.Abstractions;
using WaferAligner.Communication.Inovance;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WaferAligner
{
    /// <summary>
    /// 静态工具类 - 已弃用
    /// 请使用IPlcVariableService替代
    /// </summary>
    [Obsolete("StaticUtility已弃用，请使用IPlcVariableService和IPlcConnectionManager替代", false)]
    public class StaticUtility
    {
        [Obsolete("PLC_INSTANCE已弃用，请使用IPlcConnectionManager.GetPlcInstance()替代", false)]
        public static IPlcInstance PLC_INSTANCE = new InvoancePlcInstance();

        [Obsolete("WritePLCVariable已弃用，请使用IPlcVariableService.WriteVariableSafelyAsync替代", false)]
        public static async Task WritePLCVariable(string name, object value)
        {
            var ret = await Task.Run<bool>(async () =>
            {
                return await PLC_INSTANCE.WriteVariableAsync(new PLCVarWriteInfo { Name = name, Value = value }, cancle: CancellationToken.None);
            });

#if DEBUG
            if (!ret)
            {
                // handle error
            }
#endif
        }
    }
}
