{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\plccommunication\\plccommunication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\plccommunication\\plccommunication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\plcvariableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\plcvariableservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\mainwindowviewmodelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\mainwindowviewmodelservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\interfaces\\iplcconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\interfaces\\iplcconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\inovanceplc\\viewmodel\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\inovanceplc\\viewmodel\\mainwindowviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\inovanceplc\\axis\\xyraxisviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\inovanceplc\\axis\\xyraxisviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\inovanceplc\\axis\\commonaxis.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\inovanceplc\\axis\\commonaxis.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\ftitlepage3.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\ftitlepage3.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\ftitlepage2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\ftitlepage2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\ftitlepage1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\ftitlepage1.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\customcontro\\axiscontrol.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\customcontro\\axiscontrol.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\fheadermainfooter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\fheadermainfooter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\factories\\axisviewmodelfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\factories\\axisviewmodelfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\serviceconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\serviceconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\plcconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\plcconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\Infrastructure\\WaferAligner.Infrastructure.Configuration\\WaferAligner.Infrastructure.Configuration.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\src\\infrastructure\\waferaligner.infrastructure.configuration\\jsonfileconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|src\\Infrastructure\\WaferAligner.Infrastructure.Configuration\\WaferAligner.Infrastructure.Configuration.csproj|solutionrelative:src\\infrastructure\\waferaligner.infrastructure.configuration\\jsonfileconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{22C75AED-545D-4772-8343-7434C9E6988E}|src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\src\\communication\\waferaligner.communication.inovance\\invoanceplc.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{22C75AED-545D-4772-8343-7434C9E6988E}|src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj|solutionrelative:src\\communication\\waferaligner.communication.inovance\\invoanceplc.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{22C75AED-545D-4772-8343-7434C9E6988E}|src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\src\\communication\\waferaligner.communication.inovance\\client\\plcclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{22C75AED-545D-4772-8343-7434C9E6988E}|src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj|solutionrelative:src\\communication\\waferaligner.communication.inovance\\client\\plcclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA2CA1E6-5C2B-74F2-469A-6CE3F307481B}|Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\services\\service.common\\jyj001.app.service.common\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA2CA1E6-5C2B-74F2-469A-6CE3F307481B}|Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj|solutionrelative:services\\service.common\\jyj001.app.service.common\\baserepository.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA2CA1E6-5C2B-74F2-469A-6CE3F307481B}|Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\services\\service.common\\jyj001.app.service.common\\loggingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA2CA1E6-5C2B-74F2-469A-6CE3F307481B}|Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj|solutionrelative:services\\service.common\\jyj001.app.service.common\\loggingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{20DAEF9F-DA99-46D4-BA81-A032301F0233}|WaferAligner.EventIds\\WaferAligner.EventIds.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner.eventids\\eventids.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{20DAEF9F-DA99-46D4-BA81-A032301F0233}|WaferAligner.EventIds\\WaferAligner.EventIds.csproj|solutionrelative:waferaligner.eventids\\eventids.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\models\\serialaxisviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\models\\serialaxisviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\resourcemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\resourcemanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\basepage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\basepage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\axiseventservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\axiseventservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{81242C68-8A2A-4B4F-B795-F1491B90726A}|JYJ001.App.Service.Usermanagement\\JYJ001.App.Service.Usermanagement.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\jyj001.app.service.usermanagement\\usermanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{81242C68-8A2A-4B4F-B795-F1491B90726A}|JYJ001.App.Service.Usermanagement\\JYJ001.App.Service.Usermanagement.csproj|solutionrelative:jyj001.app.service.usermanagement\\usermanagementservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\models\\cameraaxisviewmodelnew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\models\\cameraaxisviewmodelnew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\弹出窗口.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\弹出窗口.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DA2CA1E6-5C2B-74F2-469A-6CE3F307481B}|Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\services\\service.common\\jyj001.app.service.common\\jsonfileconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DA2CA1E6-5C2B-74F2-469A-6CE3F307481B}|Services\\Service.Common\\JYJ001.App.Service.Common\\JYJ001.App.Service.Common.csproj|solutionrelative:services\\service.common\\jyj001.app.service.common\\jsonfileconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\models\\zaxisviewmodelnew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\models\\zaxisviewmodelnew.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\configuration\\serialportconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\configuration\\serialportconfiguration.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\implementation\\serialconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\implementation\\serialconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\commondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\commondata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\commonfun.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\commonfun.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\inovanceplc\\viewmodel\\control.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\inovanceplc\\viewmodel\\control.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{81242C68-8A2A-4B4F-B795-F1491B90726A}|JYJ001.App.Service.Usermanagement\\JYJ001.App.Service.Usermanagement.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\jyj001.app.service.usermanagement\\jsonstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{81242C68-8A2A-4B4F-B795-F1491B90726A}|JYJ001.App.Service.Usermanagement\\JYJ001.App.Service.Usermanagement.csproj|solutionrelative:jyj001.app.service.usermanagement\\jsonstorageservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\inovanceplc\\axis\\genericaxis.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\inovanceplc\\axis\\genericaxis.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\floginpage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\floginpage.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\fchangepassworddialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\fchangepassworddialog.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\floginpage.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\floginpage.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\plccontrol\\plccontrolservicesextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\plccontrol\\plccontrolservicesextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\servicelifetimevalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\servicelifetimevalidator.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\baseform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\baseform.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\uiupdateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\uiupdateservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\interfaces\\iserialaxisviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\interfaces\\iserialaxisviewmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\interfaces\\serialaxisviewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\interfaces\\serialaxisviewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\recipeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\recipeservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\models\\plcaxisviewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\models\\plcaxisviewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\services\\axisserviceextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\services\\axisserviceextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\factories\\axisviewmodelfactoryextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\factories\\axisviewmodelfactoryextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\models\\axisviewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\models\\axisviewmodelbase.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\common\\performancemonitor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\common\\performancemonitor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\serialcontrolservicesextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\serialcontrolservicesextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\serialcontroltest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\serialcontroltest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\implementation\\serialcomwrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\implementation\\serialcomwrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\implementation\\serialaxiscontrollerfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\implementation\\serialaxiscontrollerfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\implementation\\serialaxiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\implementation\\serialaxiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\interfaces\\iserialconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\interfaces\\iserialconnectionmanager.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\interfaces\\iserialaxiscontrollerfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\interfaces\\iserialaxiscontrollerfactory.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\interfaces\\iserialaxiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\interfaces\\iserialaxiscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\serialcontrol\\interfaces\\iserialcomwrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\serialcontrol\\interfaces\\iserialcomwrapper.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|c:\\users\\<USER>\\desktop\\waferaligner-0717-3.9.3-begin\\waferaligner\\forms\\customcontro\\axiscontrol.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\customcontro\\axiscontrol.designer.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\waferaligner\\forms\\pages\\ftitlepage2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form", "RelativeMoniker": "D:0:0:{DAA8232C-700A-4341-9978-38D02DE3FEE4}|WaferAligner\\WaferAligner.csproj|solutionrelative:waferaligner\\forms\\pages\\ftitlepage2.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}|Form"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3\\WaferAligner\\Tests\\AxisInterfaceTests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\Client\\PLCClient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:PLC\\PLC.Inovance\\Client\\PLCClient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\InvoancePLC.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:PLC\\PLC.Inovance\\InvoancePLC.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\Symbols.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:PLC\\PLC.Inovance\\Symbols.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 156, "SelectedChildIndex": 4, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "MainWindowViewModelService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\MainWindowViewModelService.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\MainWindowViewModelService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\MainWindowViewModelService.cs", "RelativeToolTip": "WaferAligner\\Services\\MainWindowViewModelService.cs", "ViewState": "AgIAAL4AAAAAAAAAAAAwwM8AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:33:53.189Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "IPlcConnectionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Interfaces\\IPlcConnectionManager.cs", "RelativeDocumentMoniker": "WaferAligner\\Interfaces\\IPlcConnectionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Interfaces\\IPlcConnectionManager.cs", "RelativeToolTip": "WaferAligner\\Interfaces\\IPlcConnectionManager.cs", "ViewState": "AgIAAAgAAAAAAAAAAAAQwBMAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:33:39.206Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "MainWindowViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\ViewModel\\MainWindowViewModel.cs", "RelativeDocumentMoniker": "WaferAligner\\InovancePLC\\ViewModel\\MainWindowViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\ViewModel\\MainWindowViewModel.cs", "RelativeToolTip": "WaferAligner\\InovancePLC\\ViewModel\\MainWindowViewModel.cs", "ViewState": "AgIAABYAAAAAAAAAAAA/wCcAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:33:30.14Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "FTitlePage2.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage2.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FTitlePage2.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage2.cs", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FTitlePage2.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:32:14.114Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "PlcCommunication.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\PlcCommunication\\PlcCommunication.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\PlcCommunication\\PlcCommunication.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\PlcCommunication\\PlcCommunication.cs", "RelativeToolTip": "WaferAligner\\Services\\PlcCommunication\\PlcCommunication.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAC4AAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T14:05:20.237Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PlcVariableService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\PlcVariableService.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\PlcVariableService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\PlcVariableService.cs", "RelativeToolTip": "WaferAligner\\Services\\PlcVariableService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAEAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:31:01.354Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "FTitlePage1.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage1.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FTitlePage1.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage1.cs", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FTitlePage1.cs", "ViewState": "AgIAALYBAAAAAAAAAAAwwMcBAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:07:49.289Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AxisControl.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\CustomContro\\AxisControl.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\CustomContro\\AxisControl.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\CustomContro\\AxisControl.cs", "RelativeToolTip": "WaferAligner\\Forms\\CustomContro\\AxisControl.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAlAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T13:43:42.538Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "FHeaderMainFooter.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\FHeaderMainFooter.cs", "RelativeDocumentMoniker": "WaferAligner\\FHeaderMainFooter.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\FHeaderMainFooter.cs", "RelativeToolTip": "WaferAligner\\FHeaderMainFooter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:47:56.423Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "FTitlePage3.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage3.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FTitlePage3.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage3.cs", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FTitlePage3.cs", "ViewState": "AgIAAJUCAAAAAAAAAAAswKUCAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T03:56:47.304Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "CommonAxis.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\Axis\\CommonAxis.cs", "RelativeDocumentMoniker": "WaferAligner\\InovancePLC\\Axis\\CommonAxis.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\Axis\\CommonAxis.cs", "RelativeToolTip": "WaferAligner\\InovancePLC\\Axis\\CommonAxis.cs", "ViewState": "AgIAACAAAAAAAAAAAAAkwFkAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T14:23:24.259Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "XyrAxisViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\Axis\\XyrAxisViewModel.cs", "RelativeDocumentMoniker": "WaferAligner\\InovancePLC\\Axis\\XyrAxisViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\Axis\\XyrAxisViewModel.cs", "RelativeToolTip": "WaferAligner\\InovancePLC\\Axis\\XyrAxisViewModel.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T14:17:28.964Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "AxisViewModelFactory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Factories\\AxisViewModelFactory.cs", "RelativeDocumentMoniker": "WaferAligner\\Factories\\AxisViewModelFactory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Factories\\AxisViewModelFactory.cs", "RelativeToolTip": "WaferAligner\\Factories\\AxisViewModelFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:31:24.354Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PlcConnectionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\PlcConnectionManager.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\PlcConnectionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\PlcConnectionManager.cs", "RelativeToolTip": "WaferAligner\\Services\\PlcConnectionManager.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAwwBQAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:31:10.392Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "JsonFileConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Infrastructure\\WaferAligner.Infrastructure.Configuration\\JsonFileConfiguration.cs", "RelativeDocumentMoniker": "src\\Infrastructure\\WaferAligner.Infrastructure.Configuration\\JsonFileConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Infrastructure\\WaferAligner.Infrastructure.Configuration\\JsonFileConfiguration.cs", "RelativeToolTip": "src\\Infrastructure\\WaferAligner.Infrastructure.Configuration\\JsonFileConfiguration.cs", "ViewState": "AgIAAAIAAAAAAAAAAAAswBQAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:12:36.639Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "InvoancePLC.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\InvoancePLC.cs", "RelativeDocumentMoniker": "src\\Communication\\WaferAligner.Communication.Inovance\\InvoancePLC.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\InvoancePLC.cs", "RelativeToolTip": "src\\Communication\\WaferAligner.Communication.Inovance\\InvoancePLC.cs", "ViewState": "AgIAAKABAAAAAAAAAAAQwLIBAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:16:49.73Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "PLCClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\Client\\PLCClient.cs", "RelativeDocumentMoniker": "src\\Communication\\WaferAligner.Communication.Inovance\\Client\\PLCClient.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\Client\\PLCClient.cs", "RelativeToolTip": "src\\Communication\\WaferAligner.Communication.Inovance\\Client\\PLCClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAYAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T09:03:05.443Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "BaseRepository.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common\\BaseRepository.cs", "RelativeDocumentMoniker": "Services\\Service.Common\\JYJ001.App.Service.Common\\BaseRepository.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common\\BaseRepository.cs", "RelativeToolTip": "Services\\Service.Common\\JYJ001.App.Service.Common\\BaseRepository.cs", "ViewState": "AgIAAAsAAAAAAAAAAAAowBIAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-30T03:52:20.206Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "ServiceConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\ServiceConfiguration.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\ServiceConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\ServiceConfiguration.cs", "RelativeToolTip": "WaferAligner\\Common\\ServiceConfiguration.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T15:07:56.211Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 65, "Title": "PLCClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\Client\\PLCClient.cs", "RelativeDocumentMoniker": "PLC\\PLC.Inovance\\Client\\PLCClient.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\Client\\PLCClient.cs", "RelativeToolTip": "PLC\\PLC.Inovance\\Client\\PLCClient.cs", "ViewState": "AgIAAAMCAAAAAAAAAAASwBQCAAB+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T08:14:34.305Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "LoggingService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common\\LoggingService.cs", "RelativeDocumentMoniker": "Services\\Service.Common\\JYJ001.App.Service.Common\\LoggingService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common\\LoggingService.cs", "RelativeToolTip": "Services\\Service.Common\\JYJ001.App.Service.Common\\LoggingService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:26:30.364Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "BasePage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\BasePage.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\BasePage.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\BasePage.cs", "RelativeToolTip": "WaferAligner\\Common\\BasePage.cs", "ViewState": "AgIAAOcCAAAAAAAAAAAEwPcCAAB/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T13:54:38.889Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "EventIds.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\EventIds.cs", "RelativeDocumentMoniker": "WaferAligner.EventIds\\EventIds.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\EventIds.cs", "RelativeToolTip": "WaferAligner.EventIds\\EventIds.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAK4CAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:07:42.651Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "ResourceManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\ResourceManager.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\ResourceManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\ResourceManager.cs", "RelativeToolTip": "WaferAligner\\Common\\ResourceManager.cs", "ViewState": "AgIAAMQBAAAAAAAAAAAEwNQBAABpAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:07:45.823Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "SerialAxisViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Models\\SerialAxisViewModel.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Models\\SerialAxisViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Models\\SerialAxisViewModel.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Models\\SerialAxisViewModel.cs", "ViewState": "AgIAAG8CAAAAAAAAAAASwIACAABwAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:30:01.824Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "AxisEventService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\AxisEventService.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\AxisEventService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\AxisEventService.cs", "RelativeToolTip": "WaferAligner\\Services\\AxisEventService.cs", "ViewState": "AgIAABECAAAAAAAAAAAEwCECAABqAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T10:36:37.306Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Program.cs", "RelativeDocumentMoniker": "WaferAligner\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Program.cs", "RelativeToolTip": "WaferAligner\\Program.cs", "ViewState": "AgIAACMAAAAAAAAAAAASwDQAAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:49:56.235Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "UserManagementService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\JYJ001.App.Service.Usermanagement\\UserManagementService.cs", "RelativeDocumentMoniker": "JYJ001.App.Service.Usermanagement\\UserManagementService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\JYJ001.App.Service.Usermanagement\\UserManagementService.cs", "RelativeToolTip": "JYJ001.App.Service.Usermanagement\\UserManagementService.cs", "ViewState": "AgIAABgBAAAAAAAAAAAEwCgBAABVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T01:48:47.024Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "CameraAxisViewModelNew.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\CameraAxisViewModelNew.cs", "RelativeDocumentMoniker": "WaferAligner\\Models\\CameraAxisViewModelNew.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\CameraAxisViewModelNew.cs", "RelativeToolTip": "WaferAligner\\Models\\CameraAxisViewModelNew.cs", "ViewState": "AgIAAGQAAAAAAAAAAAAswHQAAAB0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:24:31.152Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "弹出窗口.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\弹出窗口.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\弹出窗口.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\弹出窗口.cs", "RelativeToolTip": "WaferAligner\\Common\\弹出窗口.cs", "ViewState": "AgIAAMIAAAAAAAAAAAAswNIAAABfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T02:08:59.869Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "JsonFileConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common\\JsonFileConfiguration.cs", "RelativeDocumentMoniker": "Services\\Service.Common\\JYJ001.App.Service.Common\\JsonFileConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common\\JsonFileConfiguration.cs", "RelativeToolTip": "Services\\Service.Common\\JYJ001.App.Service.Common\\JsonFileConfiguration.cs", "ViewState": "AgIAADwAAAAAAAAAAAAwwE0AAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-29T01:30:48.657Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "ZAxisViewModelNew.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\ZAxisViewModelNew.cs", "RelativeDocumentMoniker": "WaferAligner\\Models\\ZAxisViewModelNew.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\ZAxisViewModelNew.cs", "RelativeToolTip": "WaferAligner\\Models\\ZAxisViewModelNew.cs", "ViewState": "AgIAAA8AAAAAAAAAAAAAAA0AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:24:56.224Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "SerialConnectionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialConnectionManager.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Implementation\\SerialConnectionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialConnectionManager.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Implementation\\SerialConnectionManager.cs", "ViewState": "AgIAANsAAAAAAAAAAAAwwO4AAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:29:35.1Z"}, {"$type": "Document", "DocumentIndex": 66, "Title": "InvoancePLC.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\InvoancePLC.cs", "RelativeDocumentMoniker": "PLC\\PLC.Inovance\\InvoancePLC.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\InvoancePLC.cs", "RelativeToolTip": "PLC\\PLC.Inovance\\InvoancePLC.cs", "ViewState": "AgIAAEUAAAAAAAAAAAAwwFYAAABmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T01:26:46.942Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "SerialPortConfiguration.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Configuration\\SerialPortConfiguration.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Configuration\\SerialPortConfiguration.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Configuration\\SerialPortConfiguration.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Configuration\\SerialPortConfiguration.cs", "ViewState": "AgIAAIwAAAAAAAAAAAAUwLMAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T14:17:45.059Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "CommonFun.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\CommonFun.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\CommonFun.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\CommonFun.cs", "RelativeToolTip": "WaferAligner\\Common\\CommonFun.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T11:51:28.103Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "CommonData.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\CommonData.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\CommonData.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\CommonData.cs", "RelativeToolTip": "WaferAligner\\Common\\CommonData.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T11:51:38.528Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "Control.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\ViewModel\\Control.cs", "RelativeDocumentMoniker": "WaferAligner\\InovancePLC\\ViewModel\\Control.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\ViewModel\\Control.cs", "RelativeToolTip": "WaferAligner\\InovancePLC\\ViewModel\\Control.cs", "ViewState": "AgIAAMsBAAAAAAAAAAAwwNwBAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T03:18:46.963Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "GenericAxis.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\Axis\\GenericAxis.cs", "RelativeDocumentMoniker": "WaferAligner\\InovancePLC\\Axis\\GenericAxis.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\InovancePLC\\Axis\\GenericAxis.cs", "RelativeToolTip": "WaferAligner\\InovancePLC\\Axis\\GenericAxis.cs", "ViewState": "AgIAABYBAAAAAAAAAAAwwCgBAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-20T14:23:30.284Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "JsonStorageService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\JYJ001.App.Service.Usermanagement\\JsonStorageService.cs", "RelativeDocumentMoniker": "JYJ001.App.Service.Usermanagement\\JsonStorageService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\JYJ001.App.Service.Usermanagement\\JsonStorageService.cs", "RelativeToolTip": "JYJ001.App.Service.Usermanagement\\JsonStorageService.cs", "ViewState": "AgIAAIYAAAAAAAAAAAAswJYAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T01:36:06.519Z"}, {"$type": "Document", "DocumentIndex": 67, "Title": "Symbols.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\Symbols.cs", "RelativeDocumentMoniker": "PLC\\PLC.Inovance\\Symbols.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\PLC\\PLC.Inovance\\Symbols.cs", "RelativeToolTip": "PLC\\PLC.Inovance\\Symbols.cs", "ViewState": "AgIAAMoAAAAAAAAAAAAAAN0AAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-28T01:29:56.253Z"}, {"$type": "Document", "DocumentIndex": 42, "Title": "ServiceLifetimeValidator.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\ServiceLifetimeValidator.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\ServiceLifetimeValidator.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\ServiceLifetimeValidator.cs", "RelativeToolTip": "WaferAligner\\Common\\ServiceLifetimeValidator.cs", "ViewState": "AgIAABkAAAAAAAAAAAAwwCoAAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:16:33.898Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "FLoginPage.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FLoginPage.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FLoginPage.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FLoginPage.cs", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FLoginPage.cs", "ViewState": "AgIAAMsAAAAAAAAAAIAywNkAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:48:02.15Z"}, {"$type": "Document", "DocumentIndex": 40, "Title": "FLoginPage.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FLoginPage.Designer.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FLoginPage.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FLoginPage.Designer.cs", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FLoginPage.Designer.cs", "ViewState": "AgIAABAAAAAAAAAAAAAswCAAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:45:04.31Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "FChangePasswordDialog.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FChangePasswordDialog.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FChangePasswordDialog.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FChangePasswordDialog.cs", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FChangePasswordDialog.cs", "ViewState": "AgIAAIkAAAAAAAAAAAAwwJoAAAB/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:48:31.145Z"}, {"$type": "Document", "DocumentIndex": 41, "Title": "PLCControlServicesExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\PLCControl\\PLCControlServicesExtensions.cs", "RelativeDocumentMoniker": "WaferAligner\\PLCControl\\PLCControlServicesExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\PLCControl\\PLCControlServicesExtensions.cs", "RelativeToolTip": "WaferAligner\\PLCControl\\PLCControlServicesExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-25T08:17:17.889Z"}, {"$type": "Document", "DocumentIndex": 43, "Title": "BaseForm.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\BaseForm.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\BaseForm.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\BaseForm.cs", "RelativeToolTip": "WaferAligner\\Common\\BaseForm.cs", "ViewState": "AgIAAGcAAAAAAAAAAAAowHYAAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T08:07:52.956Z"}, {"$type": "Document", "DocumentIndex": 45, "Title": "ISerialAxisViewModel.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Interfaces\\ISerialAxisViewModel.cs", "RelativeDocumentMoniker": "WaferAligner\\Interfaces\\ISerialAxisViewModel.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Interfaces\\ISerialAxisViewModel.cs", "RelativeToolTip": "WaferAligner\\Interfaces\\ISerialAxisViewModel.cs", "ViewState": "AgIAACAAAAAAAAAAAAAgwAwAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T01:03:37.156Z"}, {"$type": "Document", "DocumentIndex": 44, "Title": "UIUpdateService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\UIUpdateService.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\UIUpdateService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\UIUpdateService.cs", "RelativeToolTip": "WaferAligner\\Services\\UIUpdateService.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACgAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T07:12:04.581Z"}, {"$type": "Document", "DocumentIndex": 46, "Title": "SerialAxisViewModelBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Interfaces\\SerialAxisViewModelBase.cs", "RelativeDocumentMoniker": "WaferAligner\\Interfaces\\SerialAxisViewModelBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Interfaces\\SerialAxisViewModelBase.cs", "RelativeToolTip": "WaferAligner\\Interfaces\\SerialAxisViewModelBase.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T01:03:38.069Z"}, {"$type": "Document", "DocumentIndex": 47, "Title": "RecipeService.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\RecipeService.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\RecipeService.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\RecipeService.cs", "RelativeToolTip": "WaferAligner\\Services\\RecipeService.cs", "ViewState": "AgIAAIIBAAAAAAAAAAAwwKIBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T06:18:08.025Z"}, {"$type": "Document", "DocumentIndex": 64, "Title": "AxisInterfaceTests.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3\\WaferAligner\\Tests\\AxisInterfaceTests.cs", "RelativeDocumentMoniker": "..\\WaferAligner-0717-3.9.3\\WaferAligner\\Tests\\AxisInterfaceTests.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3\\WaferAligner\\Tests\\AxisInterfaceTests.cs", "RelativeToolTip": "..\\WaferAligner-0717-3.9.3\\WaferAligner\\Tests\\AxisInterfaceTests.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAwwGEAAABjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T03:46:31.639Z"}, {"$type": "Document", "DocumentIndex": 48, "Title": "PlcAxisViewModelBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\PlcAxisViewModelBase.cs", "RelativeDocumentMoniker": "WaferAligner\\Models\\PlcAxisViewModelBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\PlcAxisViewModelBase.cs", "RelativeToolTip": "WaferAligner\\Models\\PlcAxisViewModelBase.cs", "ViewState": "AgIAANMBAAAAAAAAAAAgwOgBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:53:53.927Z"}, {"$type": "Document", "DocumentIndex": 49, "Title": "AxisServiceExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\AxisServiceExtensions.cs", "RelativeDocumentMoniker": "WaferAligner\\Services\\AxisServiceExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Services\\AxisServiceExtensions.cs", "RelativeToolTip": "WaferAligner\\Services\\AxisServiceExtensions.cs", "ViewState": "AgIAACAAAAAAAAAAAAAQwDkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:58:24.833Z"}, {"$type": "Document", "DocumentIndex": 50, "Title": "AxisViewModelFactoryExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Factories\\AxisViewModelFactoryExtensions.cs", "RelativeDocumentMoniker": "WaferAligner\\Factories\\AxisViewModelFactoryExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Factories\\AxisViewModelFactoryExtensions.cs", "RelativeToolTip": "WaferAligner\\Factories\\AxisViewModelFactoryExtensions.cs", "ViewState": "AgIAACQAAAAAAAAAAAAwwDUAAABvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:51:14.597Z"}, {"$type": "Document", "DocumentIndex": 51, "Title": "AxisViewModelBase.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\AxisViewModelBase.cs", "RelativeDocumentMoniker": "WaferAligner\\Models\\AxisViewModelBase.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Models\\AxisViewModelBase.cs", "RelativeToolTip": "WaferAligner\\Models\\AxisViewModelBase.cs", "ViewState": "AgIAADgAAAAAAAAAAAA1wEkAAAAJAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T13:21:26.195Z"}, {"$type": "Document", "DocumentIndex": 52, "Title": "PerformanceMonitor.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\PerformanceMonitor.cs", "RelativeDocumentMoniker": "WaferAligner\\Common\\PerformanceMonitor.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Common\\PerformanceMonitor.cs", "RelativeToolTip": "WaferAligner\\Common\\PerformanceMonitor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T02:26:13.96Z"}, {"$type": "Document", "DocumentIndex": 53, "Title": "SerialControlServicesExtensions.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\SerialControlServicesExtensions.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\SerialControlServicesExtensions.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\SerialControlServicesExtensions.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\SerialControlServicesExtensions.cs", "ViewState": "AgIAACUAAAAAAAAAAAAewDcAAAAXAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T09:14:23.422Z"}, {"$type": "Document", "DocumentIndex": 55, "Title": "SerialComWrapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialComWrapper.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Implementation\\SerialComWrapper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialComWrapper.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Implementation\\SerialComWrapper.cs", "ViewState": "AgIAAEwBAAAAAAAAAAAswGABAABgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:28:48.784Z"}, {"$type": "Document", "DocumentIndex": 54, "Title": "SerialControlTest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\SerialControlTest.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\SerialControlTest.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\SerialControlTest.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\SerialControlTest.cs", "ViewState": "AgIAAEEAAAAAAAAAAAAwwFgAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T09:14:00.105Z"}, {"$type": "Document", "DocumentIndex": 56, "Title": "SerialAxisControllerFactory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialAxisControllerFactory.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Implementation\\SerialAxisControllerFactory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialAxisControllerFactory.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Implementation\\SerialAxisControllerFactory.cs", "ViewState": "AgIAAGcAAAAAAAAAAAAwwHgAAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:29:24.291Z"}, {"$type": "Document", "DocumentIndex": 57, "Title": "SerialAxisController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialAxisController.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Implementation\\SerialAxisController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Implementation\\SerialAxisController.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Implementation\\SerialAxisController.cs", "ViewState": "AgIAAOwBAAAAAAAAAAAswAACAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:29:01.094Z"}, {"$type": "Document", "DocumentIndex": 58, "Title": "ISerialConnectionManager.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialConnectionManager.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Interfaces\\ISerialConnectionManager.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialConnectionManager.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Interfaces\\ISerialConnectionManager.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:28:21.094Z"}, {"$type": "Document", "DocumentIndex": 59, "Title": "ISerialAxisControllerFactory.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialAxisControllerFactory.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Interfaces\\ISerialAxisControllerFactory.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialAxisControllerFactory.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Interfaces\\ISerialAxisControllerFactory.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:28:05.376Z"}, {"$type": "Document", "DocumentIndex": 60, "Title": "ISerialAxisController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialAxisController.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Interfaces\\ISerialAxisController.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialAxisController.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Interfaces\\ISerialAxisController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:27:46.161Z"}, {"$type": "Document", "DocumentIndex": 61, "Title": "ISerialComWrapper.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialComWrapper.cs", "RelativeDocumentMoniker": "WaferAligner\\SerialControl\\Interfaces\\ISerialComWrapper.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\SerialControl\\Interfaces\\ISerialComWrapper.cs", "RelativeToolTip": "WaferAligner\\SerialControl\\Interfaces\\ISerialComWrapper.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-18T08:27:29.975Z"}, {"$type": "Document", "DocumentIndex": 62, "Title": "AxisControl.Designer.cs", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\CustomContro\\AxisControl.Designer.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\CustomContro\\AxisControl.Designer.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\CustomContro\\AxisControl.Designer.cs", "RelativeToolTip": "WaferAligner\\Forms\\CustomContro\\AxisControl.Designer.cs", "ViewState": "AgIAAEMAAAAAAAAAAAAewFMAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T14:14:45.372Z"}, {"$type": "Document", "DocumentIndex": 63, "Title": "FTitlePage2.cs [设计]", "DocumentMoniker": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage2.cs", "RelativeDocumentMoniker": "WaferAligner\\Forms\\Pages\\FTitlePage2.cs", "ToolTip": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner\\Forms\\Pages\\FTitlePage2.cs [设计]", "RelativeToolTip": "WaferAligner\\Forms\\Pages\\FTitlePage2.cs [设计]", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-17T03:47:37.179Z", "EditorCaption": " [设计]"}]}]}]}