<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net6.0-windows</TargetFrameworks>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WaferAligner.Infrastructure.Logging\WaferAligner.Infrastructure.Logging.csproj" />
    <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\JYJ001.App.Service.Common.Extension\JYJ001.App.Service.Common.Extension.csproj" />
  </ItemGroup>

</Project>
