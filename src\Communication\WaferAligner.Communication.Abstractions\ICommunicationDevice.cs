using System;
using System.Threading;
using System.Threading.Tasks;

namespace WaferAligner.Communication.Abstractions
{
    /// <summary>
    /// 通信设备基础接口
    /// 定义所有通信设备（PLC、串口等）的通用操作
    /// </summary>
    public interface ICommunicationDevice : IDisposable
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        string DeviceName { get; }
        
        /// <summary>
        /// 设备类型
        /// </summary>
        CommunicationDeviceType DeviceType { get; }
        
        /// <summary>
        /// 是否已连接
        /// </summary>
        bool IsConnected { get; }
        
        /// <summary>
        /// 连接状态变化事件
        /// </summary>
        event EventHandler<CommunicationConnectionEventArgs> ConnectionStateChanged;
        
        /// <summary>
        /// 数据接收事件
        /// </summary>
        event EventHandler<CommunicationDataEventArgs> DataReceived;
        
        /// <summary>
        /// 错误发生事件
        /// </summary>
        event EventHandler<CommunicationErrorEventArgs> ErrorOccurred;
        
        /// <summary>
        /// 初始化设备
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> InitializeAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 连接设备
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> ConnectAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 断开连接
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> DisconnectAsync(CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 发送数据
        /// </summary>
        /// <param name="data">要发送的数据</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<bool> SendDataAsync(byte[] data, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 接收数据
        /// </summary>
        /// <param name="timeout">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        Task<byte[]> ReceiveDataAsync(int timeout = 5000, CancellationToken cancellationToken = default);
        
        /// <summary>
        /// 检查设备健康状态
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        Task<CommunicationHealthStatus> CheckHealthAsync(CancellationToken cancellationToken = default);
    }
    
    /// <summary>
    /// 通信设备类型
    /// </summary>
    public enum CommunicationDeviceType
    {
        /// <summary>
        /// 未知类型
        /// </summary>
        Unknown = 0,
        
        /// <summary>
        /// PLC设备
        /// </summary>
        PLC = 1,
        
        /// <summary>
        /// 串口设备
        /// </summary>
        Serial = 2,
        
        /// <summary>
        /// 以太网设备
        /// </summary>
        Ethernet = 3,
        
        /// <summary>
        /// USB设备
        /// </summary>
        USB = 4
    }
    
    /// <summary>
    /// 通信健康状态
    /// </summary>
    public enum CommunicationHealthStatus
    {
        /// <summary>
        /// 健康
        /// </summary>
        Healthy = 0,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 3
    }
    
    /// <summary>
    /// 通信连接事件参数
    /// </summary>
    public class CommunicationConnectionEventArgs : EventArgs
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 是否已连接
        /// </summary>
        public bool IsConnected { get; set; }
        
        /// <summary>
        /// 连接时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 附加信息
        /// </summary>
        public string Message { get; set; }
    }
    
    /// <summary>
    /// 通信数据事件参数
    /// </summary>
    public class CommunicationDataEventArgs : EventArgs
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 数据内容
        /// </summary>
        public byte[] Data { get; set; }
        
        /// <summary>
        /// 数据长度
        /// </summary>
        public int Length => Data?.Length ?? 0;
        
        /// <summary>
        /// 接收时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 数据方向（发送/接收）
        /// </summary>
        public CommunicationDataDirection Direction { get; set; }
    }
    
    /// <summary>
    /// 通信错误事件参数
    /// </summary>
    public class CommunicationErrorEventArgs : EventArgs
    {
        /// <summary>
        /// 设备名称
        /// </summary>
        public string DeviceName { get; set; }
        
        /// <summary>
        /// 错误代码
        /// </summary>
        public int ErrorCode { get; set; }
        
        /// <summary>
        /// 错误消息
        /// </summary>
        public string ErrorMessage { get; set; }
        
        /// <summary>
        /// 异常对象
        /// </summary>
        public Exception Exception { get; set; }
        
        /// <summary>
        /// 错误时间
        /// </summary>
        public DateTime Timestamp { get; set; } = DateTime.Now;
        
        /// <summary>
        /// 错误严重程度
        /// </summary>
        public CommunicationErrorSeverity Severity { get; set; }
    }
    
    /// <summary>
    /// 数据方向
    /// </summary>
    public enum CommunicationDataDirection
    {
        /// <summary>
        /// 发送
        /// </summary>
        Send = 0,
        
        /// <summary>
        /// 接收
        /// </summary>
        Receive = 1
    }
    
    /// <summary>
    /// 错误严重程度
    /// </summary>
    public enum CommunicationErrorSeverity
    {
        /// <summary>
        /// 信息
        /// </summary>
        Information = 0,
        
        /// <summary>
        /// 警告
        /// </summary>
        Warning = 1,
        
        /// <summary>
        /// 错误
        /// </summary>
        Error = 2,
        
        /// <summary>
        /// 严重错误
        /// </summary>
        Critical = 3
    }
}
