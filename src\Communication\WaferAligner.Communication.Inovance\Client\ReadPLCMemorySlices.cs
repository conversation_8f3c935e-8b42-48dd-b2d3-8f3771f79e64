﻿namespace WaferAligner.Communication.Inovance.Client
{
    public class ReadPLCMemorySlices
    {
        public List<MemorySliceInfo> Buffer = new();

        public int interval = 100;

        public bool Insert(uint handler, int startAddr, int offset)
        {
            var quotient = startAddr / interval;
            var slice = this.Buffer.Find(item => item.Base == quotient);
            if (slice != null)
            {
                slice.Insert(handler, startAddr, offset);
            }
            else
            {
                var new_slice = new MemorySliceInfo(handler, startAddr, offset, quotient);
                this.Buffer.Add(new_slice);
            }
            return true;
        }

        public bool Remove(uint handler, int addr)
        {
            var quotient = addr / interval;
            var slice = this.Buffer.Find(item => item.Base == quotient);
            slice.Remove(handler, addr, 0);
            return true;
        }
    }
}
