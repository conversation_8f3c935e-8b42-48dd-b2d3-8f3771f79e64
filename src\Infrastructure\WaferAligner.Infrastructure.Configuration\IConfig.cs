using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace WaferAligner.Infrastructure.Configuration
{
    public interface IConfig
    {
        event EventHandler ConfigurationChangeEvent;
        event EventHandler ConfigurationReloadEvent;
        void AddOrUpdate(string name, object value);
        object GetValue(string name, string mode);
        void SaveConfiguration();
        Task<bool> LoadConfigurationAsync(string loadPath, IDictionary<string, object> map, Action executor);
        bool LoadConfiguration(string loadPath, IDictionary<string, object> map);
        bool ReLoadConfiguration(string loadPath);
        void ImplementConfiguration();
    }


    public static class ConfigurationExtension
    {
        public static object GetSystemValue(this IConfig config, string name)
        {
            return config.GetValue(name, "systerm");
        }
        public static object GetEquipmentValue(this IConfig config, string name)
        {
            return config.GetValue(name, "equipment");
        }

        public static bool TryGetSystemValue(this IConfig config, string name, out object value)
        {
            value = config.GetValue(name, "systerm");
            if (value == null)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
        public static bool TryGetEquipmentValue(this IConfig config, string name, out object value)
        {
            value = config.GetValue(name, "equipment");
            if (value == null)
            {
                return false;
            }
            else
            {
                return true;
            }
        }
    }
}
