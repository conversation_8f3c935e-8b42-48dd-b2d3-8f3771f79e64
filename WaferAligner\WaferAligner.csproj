﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows7.0</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <ApplicationIcon>gerber.ico</ApplicationIcon>
    <Platforms>AnyCPU;x86</Platforms>
    <BaseOutputPath>C:\对准设备2\对准设备软件\WaferAligner</BaseOutputPath>
  </PropertyGroup>

  <ItemGroup>
    <Content Include="gerber.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
    <PackageReference Include="Moq" Version="4.20.72" />
    <PackageReference Include="MSTest.TestAdapter" Version="3.9.3" />
    <PackageReference Include="MSTest.TestFramework" Version="3.9.3" />
    <PackageReference Include="SunnyUI" Version="*******" />
    <PackageReference Include="SunnyUI.Common" Version="3.8.0" />
    <PackageReference Include="System.IO.Ports" Version="9.0.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\JYJ001.App.Service.Usermanagement\JYJ001.App.Service.Usermanagement.csproj" />
    <ProjectReference Include="..\src\Communication\WaferAligner.Communication.Abstractions\WaferAligner.Communication.Abstractions.csproj" />
    <ProjectReference Include="..\src\Communication\WaferAligner.Communication.Inovance\WaferAligner.Communication.Inovance.csproj" />
    <ProjectReference Include="..\Services\Service.Common\JYJ001.App.Service.Common.Extension\JYJ001.App.Service.Common.Extension.csproj" />
    <ProjectReference Include="..\Services\Service.Common\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
    <ProjectReference Include="..\Services\Service.Common\JYJ001.App.Service.Common\JYJ001.App.Service.Common.csproj" />
    <ProjectReference Include="..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
  </ItemGroup>

  <ItemGroup>

    <Reference Include="Microsoft.Extensions.Configuration">
      <HintPath>..\..\WaferAligner_Release\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>

    <Reference Include="Microsoft.Extensions.Hosting">
      <HintPath>..\..\WaferAligner_Release\Debug\net6.0-windows\Microsoft.Extensions.Hosting.dll</HintPath>
    </Reference>

    <Reference Include="Microsoft.Extensions.Logging">
      <HintPath>..\..\WaferAligner_Release\Debug\net6.0-windows\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>

    <Reference Include="MySql.Data">
      <HintPath>C:\Users\<USER>\Desktop\软件加密Demo\MySql.Data.8.0.30\lib\net6.0\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Management">
      <HintPath>..\..\WaferAligner_Release2\Debug\net6.0-windows\System.Management.dll</HintPath>
    </Reference>
    <Reference Include="Ubiety.Dns.Core">
      <HintPath>..\..\WaferAligner_Release3\Debug\net6.0-windows\Ubiety.Dns.Core.dll</HintPath>
    </Reference>
    <Reference Include="ZstdNet">
      <HintPath>..\..\WaferAligner_Release3\Debug\net6.0-windows\ZstdNet.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migration\" />
  </ItemGroup>

</Project>