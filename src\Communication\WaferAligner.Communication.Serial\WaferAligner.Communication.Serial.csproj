<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\WaferAligner.Communication.Abstractions\WaferAligner.Communication.Abstractions.csproj" />
    <ProjectReference Include="..\..\..\WaferAligner.EventIds\WaferAligner.EventIds.csproj" />
    <ProjectReference Include="..\..\..\src\Infrastructure\WaferAligner.Infrastructure.Extensions\WaferAligner.Infrastructure.Extensions.csproj" />
    <ProjectReference Include="..\..\..\src\Infrastructure\WaferAligner.Infrastructure.Logging\WaferAligner.Infrastructure.Logging.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\JYJ001.App.Service.Common.Interface\JYJ001.App.Service.Common.Interface.csproj" />
    <ProjectReference Include="..\..\..\Services\Service.Common\JYJ001.App.Service.Common.Extension\JYJ001.App.Service.Common.Extension.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="System.IO.Ports" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="9.0.6" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.6" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="6.0.0" />
  </ItemGroup>

</Project>
