﻿using System.ComponentModel;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Threading.Channels;
using WaferAligner.Infrastructure.Logging;
namespace WaferAligner.Communication.Inovance.Client
{

    public class InvoancePlcClient : IDisposable
    {
        #region //标准库
        [DllImport("StandardModbusApi.dll", EntryPoint = "Init_ETH_String", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Init_ETH_String(string sIpAddr, int nNetId = 0, int IpPort = 502);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Exit_ETH", CallingConvention = CallingConvention.Cdecl)]
        public static extern bool Exit_ETH(int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        /******************************************************************************
         1.功能描述 : 写Am600软元件
         2.返 回 值 :1 成功  0 失败
         3.参    数 : nNetId:网络链接编号
                      eType：软元件类型    ELEM_QX = 0//QX元件  ELEM_MW = 1 //MW元件
                      nStartAddr:软元件起始地址（QX元件由于带小数点，地址需要乘以10去掉小数点，如QX10.1，请输入101，MW元件直接就是它的元件地址不用处理）
                      nCount：软元件个数
                      pValue：数据缓存区
        4.注意事项 :  1.x和y元件地址需为8进制; 
                      2. 当元件位C元件双字寄存器时，每个寄存器需占4个字节的数据
                      3.如果是写位元件，每个位元件的值存储在一个字节中
        //******************************************************************************/
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int Am600_Write_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Write_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Write_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);
        /******************************************************************************
         1.功能描述 : 读Am600软元件
         2.返 回 值 :1 成功  0 失败
         3.参    数 : nNetId:网络链接编号
                      eType：软元件类型   ELEM_QX = 0//QX元件  ELEM_MW = 1 //MW元件
                      nStartAddr:软元件起始地址（QX元件由于带小数点，地址需要乘以10去掉小数点，如QX10.1，请输入101，其它元件不用处理）
                      nCount：软元件个数
                      pValue：返回数据缓存区
         4.注意事项 : 如果是读位元件，每个位元件的值存储在一个字节中，pValue数据缓存区字节数必须是8的整数倍
        ******************************************************************************/
        //[DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem", CallingConvention = CallingConvention.Cdecl)]
        //public static extern int Am600_Read_Soft_Elem(SoftElemType eType, int nStartAddr, int nCount, byte[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int16(SoftElemType eType, int nStartAddr, int nCount, short[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Int32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Int32(SoftElemType eType, int nStartAddr, int nCount, int[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt16", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt16(SoftElemType eType, int nStartAddr, int nCount, ushort[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_UInt32", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_UInt32(SoftElemType eType, int nStartAddr, int nCount, uint[] pValue, int nNetId = 0);
        [DllImport("StandardModbusApi.dll", EntryPoint = "Am600_Read_Soft_Elem_Float", CallingConvention = CallingConvention.Cdecl)]
        public static extern int Am600_Read_Soft_Elem_Float(SoftElemType eType, int nStartAddr, int nCount, float[] pValue, int nNetId = 0);
        #endregion

        static bool symbolLoaded = false;
        static readonly List<VariableSymbol> Symbols = new();
        readonly ReadPLCMemorySlices slices = new();
        CancellationTokenSource cts = new();
        private bool connected = false;
        private object LockObj = new();
        private SemaphoreSlim se = new(0);
        
        // 移除BackgroundWorker，使用Task-based异步模式
        // BackgroundWorker readWorker = new();
        private Task _readTask = null;
        private CancellationTokenSource _readTaskCts = new();
        
        // 添加日志服务成员变量
        private readonly ILoggingService _logger;
        
        //private Channel<object> cahnnel = new Channel<object>();
        public InvoancePlcClient(ILoggingService logger = null)
        {
            _logger = logger;
            this.LoadSymbols();
            // 移除BackgroundWorker事件注册
            // readWorker.DoWork += ReadVariable;
        }
        
        // 启动读取任务
        private async Task StartReadTaskAsync()
        {
            // 取消之前的任务
            if (_readTask != null)
            {
                await CancelReadTask();
            }
            
            // 创建新的取消令牌
            _readTaskCts = new CancellationTokenSource();
            
            // 启动新的读取任务
            _readTask = Task.Run(async () => await ReadVariableAsync(_readTaskCts.Token), _readTaskCts.Token);
            _logger?.LogInformation("已启动PLC变量读取任务", WaferAligner.EventIds.EventIds.Plc_Connection_Succeeded);
        }
        
        // 取消读取任务
        private async Task CancelReadTask()
        {
            if (_readTaskCts != null)
            {
                _readTaskCts.Cancel();
                try 
                {
                    if (_readTask != null)
                    {
                        await Task.WhenAny(_readTask, Task.Delay(1000));
                    }
                }
                catch (Exception ex) 
                {
                    _logger?.LogWarning($"取消PLC读取任务时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Disconnection_Completed);
                }
                _readTaskCts.Dispose();
                _readTaskCts = new CancellationTokenSource();
                _logger?.LogDebug("已取消PLC变量读取任务", WaferAligner.EventIds.EventIds.Resource_Released);
            }
        }

        // 替换原DoWork事件处理程序
        // private async void ReadVariable(object? sender, DoWorkEventArgs e)
        private async Task ReadVariableAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger?.LogInformation("PLC读取任务已启动", WaferAligner.EventIds.EventIds.Plc_Connection_Succeeded);
                
                while (!cancellationToken.IsCancellationRequested)
                {
                    try
                    {
                        var slices_len = slices.Buffer.Count;
                        if (slices_len > 0)
                        {
                            for (int i = 0; i < slices_len && !cancellationToken.IsCancellationRequested; i++)
                            {
                                var info = slices.Buffer[i].SnapShot();
                                var number = info.Item2;
                                byte[] read_buffer = new byte[number * 2];
                                //byte[] read_buffer = new byte[128];
                                var ret = Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, info.Item3, info.Item2, read_buffer);
                                foreach (var handler in info.Item1)
                                {
                                    try
                                    {
                                        var symbol = Symbols.Find(v => v.Handler == handler && v.RegistryNumber > 0);
                                        var baseaddr = (symbol.Address - info.Item3) * 2;
                                        symbol.SetValue(new Span<byte>(read_buffer, baseaddr, symbol.Size));
                                        //var v = CastValue(symbol.GetValuePointer(), symbol.DataType);
                                        var v = symbol.GetValue();
                                        this.HCNotificationChanged.Invoke(this, new InvoanceVariableChangedEventArgs(symbol.Name, handler, v));
                                    }
                                    catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
                                    {
                                        _logger?.LogError(ex, $"处理PLC变量时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                                        throw;
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex) when (!cancellationToken.IsCancellationRequested)
                    {
                        // 处理异常，但不中断循环
                        _logger?.LogError(ex, $"PLC读取循环中发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                    }
                    
                    // 等待延迟，但支持取消
                    try
                    {
                        await Task.Delay(200, cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        // 任务取消时正常结束，不记录异常
                        _logger?.LogDebug("PLC读取任务延迟被取消，准备退出循环", WaferAligner.EventIds.EventIds.Resource_Released);
                        break;
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // 任务取消是正常流程，记录信息级别日志
                _logger?.LogInformation("PLC读取任务已正常取消", WaferAligner.EventIds.EventIds.Resource_Released);
            }
            catch (Exception ex)
            {
                // 只有非取消异常才记录为错误
                if (!(ex is OperationCanceledException))
                {
                    _logger?.LogError(ex, $"PLC读取任务异常终止: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                }
            }
            finally
            {
                _logger?.LogDebug("PLC读取任务已结束", WaferAligner.EventIds.EventIds.Plc_Disconnection_Completed);
            }
        }

        // 提供连接状态查询接口
        public bool IsConnected => connected;
        
        public bool Connect(string ipAddress, int port, CancellationToken? token)
        {
            lock (LockObj)
            {
                try
                {
                    if (!connected)
                    {
                        if (Init_ETH_String(ipAddress, 0, port))
                        {
                            connected = true;
                            //thread.Start(this.cts.Token);
                            
                            // 移除BackgroundWorker
                            // if (!readWorker.IsBusy)
                            // {
                            //     readWorker.RunWorkerAsync();
                            // }
                            
                            // 启动异步读取任务
                            _ = StartReadTaskAsync();
                            _logger?.LogInformation($"已连接到PLC: {ipAddress}:{port}", WaferAligner.EventIds.EventIds.Plc_Connection_Succeeded);
                            
                            return true;
                        }
                        else
                        {
                            connected = false;
                            _logger?.LogError($"连接PLC失败: {ipAddress}:{port}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                            return false;
                        }
                    }
                    else
                    {
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    // 改进4：改进异常处理 - 不重新抛出异常
                    connected = false;
                    // 这里可以添加日志记录，但为了避免依赖，暂时使用Console输出
                    _logger?.LogError(ex, $"PLC连接异常: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Connection_Failed);
                    return false;
                }
            }
        }

        object CastValue(Span<byte> buffers, Type type)
        {
            switch (type.Name.ToLower())
            {
                case "boolean":
                    return BitConverter.ToBoolean(buffers);
                case "byte":
                    return buffers[0];
                case "sbyte":
                    return buffers[0];
                case "uint16":
                    return BitConverter.ToUInt16(buffers);
                case "int16":
                    return BitConverter.ToInt16(buffers);
                case "int32":
                    return BitConverter.ToInt32(buffers);
                case "uint32":
                    return BitConverter.ToUInt32(buffers);
                case "single":
                    return BitConverter.ToSingle(buffers);
                case "double":
                    return BitConverter.ToDouble(buffers);
                default:
                    return null;
            };
        }

        IEnumerable<VariableSymbol> ParseFromJson()
        {
            var dir_path = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData) + @"\Aligner\PLCSymbols\";
            var files = Directory.GetFiles(dir_path);
            if (files.Length <= 0)
            {
                throw new FileNotFoundException($"Can not load any symbols file in directory {dir_path}");
            }
            var symbolFilePath = files.Where(item => item.Contains(".Device.Application.json")).FirstOrDefault();
            if (symbolFilePath is null)
            {
                throw new FileNotFoundException($"Can not load any symbols file in directory {dir_path}");
            }
            var jsonStr = File.ReadAllText(symbolFilePath);
            var symbols = JsonSerializer.Deserialize<Symbols>(jsonStr);
            uint handler = 0;
            foreach (var item in symbols.GetSymbols())
            {
                if (item.Name is not null && item.Address is not null && item.Type is not null)
                {
                    var symbol = new VariableSymbol(handler++, item);
                    yield return symbol;
                }
            };
        }

        public bool KeepAliveRequest()
        {
            try
            {
                // 使用超时保护，防止阻塞UI线程
                var task = Task.Run(() => Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, 0, 1, new byte[2], 0));
                if (task.Wait(2500)) // 最多等待2.5秒，适应工业网络环境
                {
                    return task.Result == 1;
                }
                else
                {
                    // 超时，认为连接失败
                    return false;
                }
            }
            catch (Exception)
            {
                // 发生异常，认为连接失败
                return false;
            }
        }

        public void LoadSymbols()
        {
            lock (Symbols)
            {
                if (!symbolLoaded)
                {
                    foreach (var item in ParseFromJson())
                    {
                        Symbols.Add(item);
                    }
                    symbolLoaded = true;
                }
            }
        }

        public EventHandler<InvoanceVariableChangedEventArgs> HCNotificationChanged = (s, e) => { };

        public EnumErrorCode AddDeviceNotificationEx(string symbolPath, NotificationSettings settings, object? userData, Type type, out uint notificationHandler)
        {
            if (_disposed)
            {
                throw new ObjectDisposedException("客户端链接已被释放");
            }
            var s = Symbols.Find(s => s.Name == symbolPath);

            if (s != null)
            {
                if (s.Address < 100)
                {
                    notificationHandler = uint.MaxValue;
                    se.Release();
                    return EnumErrorCode.None;
                }
                notificationHandler = s.Handler;
                try
                {
                    lock (slices)
                    {
                        var ret = slices.Insert(s.Handler, s.Address, s.Size);

                        if (ret)
                        {
                            return TryAddDeviceNotificationEX(symbolPath, settings, userData, type, null);
                        }
                        else
                        {
                            return EnumErrorCode.ER_ELEM_ADDR_OVER;
                        }
                    }

                }
                catch (Exception ex)
                {
                    // 改进4：改进异常处理 - 记录但不抛出异常
                    _logger?.LogError(ex, $"添加设备通知时发生异常", WaferAligner.EventIds.EventIds.Plc_Variable_Read_Failed);
                    notificationHandler = uint.MaxValue;
                    return EnumErrorCode.ER_COMM_EXCEPT;
                }
            }
            else
            {
                notificationHandler = uint.MaxValue;
                return EnumErrorCode.ER_SYMBOLNOTFOUND;
            }
        }

        private EnumErrorCode TryAddDeviceNotificationEX(string symbolPath, NotificationSettings settings, object? userData, Type type, int[]? args)
        {
            Symbols.Find(s => s.Name == symbolPath)?.IncreaseNotificationRegistry();

            return EnumErrorCode.None;
        }
        public int WriteData(string name, object value)
        {
            VariableSymbol variableinfo = Symbols.Find(v => v.Name == name);
            if (variableinfo is null)
            {
                return 0;
            }
            var buffer = GetBytes(value, variableinfo.DataType);
            var word_size = variableinfo.Size / 2;
            if (word_size == 0)
            {
                word_size = 1;
            }
            var ret = Am600_Write_Soft_Elem(SoftElemType.ELEM_MW, variableinfo.Address, word_size, buffer, 0);
            if (ret == 0)
            {
                return 0;
            }
            else
            {
                return word_size;
            }
        }
        public object? ReadDataAsync(string name, Type type)
        {
            VariableSymbol variableinfo = Symbols.Find(v => v.Name == name);
            var buffer = new byte[8];
            var word_size = variableinfo.Size / 2;
            if (word_size == 0)
            {
                word_size = 1;
            }

            var ret = Am600_Read_Soft_Elem(SoftElemType.ELEM_MW, variableinfo.Address, word_size, buffer, 0);
            if (ret == 0)
            {
                return null;
            }
            var span = new Span<byte>(buffer, 0, variableinfo.Size);

            return CastValue(span, variableinfo.DataType);
        }

        byte[] GetBytes(object value, Type type)
        {
            switch (type.Name.ToLower())
            {
                case "boolean":
                    var bool_buffer = Convert.ToBoolean(value);
                    return BitConverter.GetBytes(bool_buffer);
                case "byte" or "sbyte":
                    var byte_value = (byte)value;
                    return new byte[] { byte_value };
                case "int16":
                    var int16_buffer = Convert.ToInt16(value);
                    return BitConverter.GetBytes(int16_buffer);
                case "uint16":
                    var uint16_buffer = Convert.ToUInt16(value);
                    return BitConverter.GetBytes(uint16_buffer);
                case "int32":
                    var int32 = Convert.ToInt32(value);
                    return BitConverter.GetBytes(int32);
                case "uint32":
                    var uint32_buffer = Convert.ToUInt32(value);
                    return BitConverter.GetBytes(uint32_buffer);
                case "int64":
                    var int64_buffer = Convert.ToInt64(value);
                    return BitConverter.GetBytes(int64_buffer);
                case "single":
                    var single_buffer = Convert.ToSingle(value);
                    return BitConverter.GetBytes(single_buffer);
                case "double":
                    var double_buffer = Convert.ToDouble(value);
                    return BitConverter.GetBytes(double_buffer);
                default:
                    return null;
            };
        }

        private bool _disposed;
        public bool isDisposed => _disposed;
        public void Dispose()
        {
            if (!_disposed)
            {
                Dispose(disposing: true);
            }
            _disposed = true;
            GC.SuppressFinalize(this);
        }
        private void Dispose(bool disposing)
        {
            if (disposing)
            {
                try
                {
                    cts.Cancel();
                    cts.Dispose();
                    
                    // 停止后台工作线程
                    // if (readWorker != null && readWorker.IsBusy)
                    // {
                    //     readWorker.CancelAsync();
                    //     // 等待一小段时间让后台线程停止
                    //     for (int i = 0; i < 10 && readWorker.IsBusy; i++)
                    //     {
                    //         System.Threading.Thread.Sleep(100);
                    //     }
                    // }
                    
                    // 使用Task-based异步模式
                    CancelReadTask().Wait(1000);
                    _logger?.LogInformation("已取消PLC读取任务并释放资源", WaferAligner.EventIds.EventIds.Resource_Released);
                    
                    Disconnect();
                }
                catch (Exception ex)
                {
                    // 改进4：改进异常处理 - 记录但不抛出异常
                    _logger?.LogError(ex, $"释放PLC客户端资源时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Unhandled_Exception);
                }
            }
        }
        private void Disconnect()
        {
            try
            {
                if (connected)
                {
                    Exit_ETH();
                    connected = false; // 确保连接状态被正确更新
                }
            }
            catch (Exception ex)
            {
                // 改进4：改进异常处理 - 记录但不抛出异常
                _logger?.LogError(ex, $"断开PLC连接时发生异常: {ex.Message}", WaferAligner.EventIds.EventIds.Plc_Disconnection_Completed);
                connected = false; // 即使出现异常也要更新状态
            }
        }
        ~InvoancePlcClient()
        {
            //20240911
            //readWorker.CancelAsync();
            Dispose(disposing: false);
        }
    }
}
