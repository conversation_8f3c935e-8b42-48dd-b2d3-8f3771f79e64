{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj", "projectName": "JYJ001.App.Business", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Business\\JYJ001.App.Business\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[7.0.0, )"}, "System.Reactive": {"target": "Package", "version": "[5.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj", "projectName": "JYJ001.App.Service.Common.Interface", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Business\\JYJ001.App.Business\\JYJ001.App.Business.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[6.0.11, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj", "projectName": "WaferAligner.Communication.Abstractions", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Abstractions\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj", "projectName": "WaferAligner.Communication.Inovance", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\WaferAligner.Communication.Inovance.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Inovance\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\Services\\Service.Common\\JYJ001.App.Service.Common.Interface\\JYJ001.App.Service.Common.Interface.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\src\\Communication\\WaferAligner.Communication.Abstractions\\WaferAligner.Communication.Abstractions.csproj"}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj", "projectName": "WaferAligner.EventIds", "projectPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\WaferAligner.EventIds.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\WaferAligner-0717-3.9.3-begin\\WaferAligner.EventIds\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}